{"version": 3, "file": "unsafe-svg.js", "sourceRoot": "", "sources": ["../../src/directives/unsafe-svg.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,SAAS,EAAC,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAC,mBAAmB,EAAC,MAAM,kBAAkB,CAAC;AAErD,MAAM,UAAU,GAAG,CAAC,CAAC;AAErB,MAAM,kBAAmB,SAAQ,mBAAmB;;AAClC,gCAAa,GAAG,WAAW,CAAC;AAC5B,6BAAU,GAAG,UAAU,CAAC;AAG1C;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,SAAS,CAAC,kBAAkB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {directive} from '../directive.js';\nimport {UnsafeHTMLDirective} from './unsafe-html.js';\n\nconst SVG_RESULT = 2;\n\nclass UnsafeSVGDirective extends UnsafeHTMLDirective {\n  static override directiveName = 'unsafeSVG';\n  static override resultType = SVG_RESULT;\n}\n\n/**\n * Renders the result as SVG, rather than text.\n *\n * The values `undefined`, `null`, and `nothing`, will all result in no content\n * (empty string) being rendered.\n *\n * Note, this is unsafe to use with any user-provided input that hasn't been\n * sanitized or escaped, as it may lead to cross-site-scripting\n * vulnerabilities.\n */\nexport const unsafeSVG = directive(UnsafeSVGDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {UnsafeSVGDirective};\n"]}