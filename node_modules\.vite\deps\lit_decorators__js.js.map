{"version": 3, "sources": ["../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/custom-element.ts", "../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/property.ts", "../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/state.ts", "../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/event-options.ts", "../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/base.ts", "../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/query.ts", "../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/query-all.ts", "../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/query-async.ts", "../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/query-assigned-elements.ts", "../../.pnpm/@lit+reactive-element@2.1.1/node_modules/@lit/reactive-element/src/decorators/query-assigned-nodes.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport type {Constructor} from './base.js';\n\n/**\n * Allow for custom element classes with private constructors\n */\ntype CustomElementClass = Omit<typeof HTMLElement, 'new'>;\n\nexport type CustomElementDecorator = {\n  // legacy\n  (cls: CustomElementClass): void;\n\n  // standard\n  (\n    target: CustomElementClass,\n    context: ClassDecoratorContext<Constructor<HTMLElement>>\n  ): void;\n};\n\n/**\n * Class decorator factory that defines the decorated class as a custom element.\n *\n * ```js\n * @customElement('my-element')\n * class MyElement extends LitElement {\n *   render() {\n *     return html``;\n *   }\n * }\n * ```\n * @category Decorator\n * @param tagName The tag name of the custom element to define.\n */\nexport const customElement =\n  (tagName: string): CustomElementDecorator =>\n  (\n    classOrTarget: CustomElementClass | Constructor<HTMLElement>,\n    context?: ClassDecoratorContext<Constructor<HTMLElement>>\n  ) => {\n    if (context !== undefined) {\n      context.addInitializer(() => {\n        customElements.define(\n          tagName,\n          classOrTarget as CustomElementConstructor\n        );\n      });\n    } else {\n      customElements.define(tagName, classOrTarget as CustomElementConstructor);\n    }\n  };\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {\n  type PropertyDeclaration,\n  type ReactiveElement,\n  defaultConverter,\n  notEqual,\n} from '../reactive-element.js';\nimport type {Interface} from './base.js';\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  globalThis.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (\n      !globalThis.litIssuedWarnings!.has(warning) &&\n      !globalThis.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      globalThis.litIssuedWarnings!.add(warning);\n    }\n  };\n}\n\n// Overloads for property decorator so that TypeScript can infer the correct\n// return type when a decorator is used as an accessor decorator or a setter\n// decorator.\nexport type PropertyDecorator = {\n  // accessor decorator signature\n  <C extends Interface<ReactiveElement>, V>(\n    target: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n\n  // setter decorator signature\n  <C extends Interface<ReactiveElement>, V>(\n    target: (value: V) => void,\n    context: ClassSetterDecoratorContext<C, V>\n  ): (this: C, value: V) => void;\n\n  // legacy decorator signature\n  (\n    protoOrDescriptor: Object,\n    name: PropertyKey,\n    descriptor?: PropertyDescriptor\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): any;\n};\n\nconst legacyProperty = (\n  options: PropertyDeclaration | undefined,\n  proto: Object,\n  name: PropertyKey\n) => {\n  const hasOwnProperty = proto.hasOwnProperty(name);\n  (proto.constructor as typeof ReactiveElement).createProperty(name, options);\n  // For accessors (which have a descriptor on the prototype) we need to\n  // return a descriptor, otherwise TypeScript overwrites the descriptor we\n  // define in createProperty() with the original descriptor. We don't do this\n  // for fields, which don't have a descriptor, because this could overwrite\n  // descriptor defined by other decorators.\n  return hasOwnProperty\n    ? Object.getOwnPropertyDescriptor(proto, name)\n    : undefined;\n};\n\n// This is duplicated from a similar variable in reactive-element.ts, but\n// actually makes sense to have this default defined with the decorator, so\n// that different decorators could have different defaults.\nconst defaultPropertyDeclaration: PropertyDeclaration = {\n  attribute: true,\n  type: String,\n  converter: defaultConverter,\n  reflect: false,\n  hasChanged: notEqual,\n};\n\n// Temporary type, until google3 is on TypeScript 5.2\ntype StandardPropertyContext<C, V> = (\n  | ClassAccessorDecoratorContext<C, V>\n  | ClassSetterDecoratorContext<C, V>\n) & {metadata: object};\n\n/**\n * Wraps a class accessor or setter so that `requestUpdate()` is called with the\n * property name and old value when the accessor is set.\n */\nexport const standardProperty = <C extends Interface<ReactiveElement>, V>(\n  options: PropertyDeclaration = defaultPropertyDeclaration,\n  target: ClassAccessorDecoratorTarget<C, V> | ((value: V) => void),\n  context: StandardPropertyContext<C, V>\n): ClassAccessorDecoratorResult<C, V> | ((this: C, value: V) => void) => {\n  const {kind, metadata} = context;\n\n  if (DEV_MODE && metadata == null) {\n    issueWarning(\n      'missing-class-metadata',\n      `The class ${target} is missing decorator metadata. This ` +\n        `could mean that you're using a compiler that supports decorators ` +\n        `but doesn't support decorator metadata, such as TypeScript 5.1. ` +\n        `Please update your compiler.`\n    );\n  }\n\n  // Store the property options\n  let properties = globalThis.litPropertyMetadata.get(metadata);\n  if (properties === undefined) {\n    globalThis.litPropertyMetadata.set(metadata, (properties = new Map()));\n  }\n  if (kind === 'setter') {\n    options = Object.create(options);\n    options.wrapped = true;\n  }\n  properties.set(context.name, options);\n\n  if (kind === 'accessor') {\n    // Standard decorators cannot dynamically modify the class, so we can't\n    // replace a field with accessors. The user must use the new `accessor`\n    // keyword instead.\n    const {name} = context;\n    return {\n      set(this: ReactiveElement, v: V) {\n        const oldValue = (\n          target as ClassAccessorDecoratorTarget<C, V>\n        ).get.call(this as unknown as C);\n        (target as ClassAccessorDecoratorTarget<C, V>).set.call(\n          this as unknown as C,\n          v\n        );\n        this.requestUpdate(name, oldValue, options);\n      },\n      init(this: ReactiveElement, v: V): V {\n        if (v !== undefined) {\n          this._$changeProperty(name, undefined, options, v);\n        }\n        return v;\n      },\n    } as unknown as ClassAccessorDecoratorResult<C, V>;\n  } else if (kind === 'setter') {\n    const {name} = context;\n    return function (this: ReactiveElement, value: V) {\n      const oldValue = this[name as keyof ReactiveElement];\n      (target as (value: V) => void).call(this, value);\n      this.requestUpdate(name, oldValue, options);\n    } as unknown as (this: C, value: V) => void;\n  }\n  throw new Error(`Unsupported decorator location: ${kind}`);\n};\n\n/**\n * A class field or accessor decorator which creates a reactive property that\n * reflects a corresponding attribute value. When a decorated property is set\n * the element will update and render. A {@linkcode PropertyDeclaration} may\n * optionally be supplied to configure property features.\n *\n * This decorator should only be used for public fields. As public fields,\n * properties should be considered as primarily settable by element users,\n * either via attribute or the property itself.\n *\n * Generally, properties that are changed by the element should be private or\n * protected fields and should use the {@linkcode state} decorator.\n *\n * However, sometimes element code does need to set a public property. This\n * should typically only be done in response to user interaction, and an event\n * should be fired informing the user; for example, a checkbox sets its\n * `checked` property when clicked and fires a `changed` event. Mutating public\n * properties should typically not be done for non-primitive (object or array)\n * properties. In other cases when an element needs to manage state, a private\n * property decorated via the {@linkcode state} decorator should be used. When\n * needed, state properties can be initialized via public properties to\n * facilitate complex interactions.\n *\n * ```ts\n * class MyElement {\n *   @property({ type: Boolean })\n *   clicked = false;\n * }\n * ```\n * @category Decorator\n * @ExportDecoratedItems\n */\nexport function property(options?: PropertyDeclaration): PropertyDecorator {\n  return <C extends Interface<ReactiveElement>, V>(\n    protoOrTarget:\n      | object\n      | ClassAccessorDecoratorTarget<C, V>\n      | ((value: V) => void),\n    nameOrContext:\n      | PropertyKey\n      | ClassAccessorDecoratorContext<C, V>\n      | ClassSetterDecoratorContext<C, V>\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): any => {\n    return (\n      typeof nameOrContext === 'object'\n        ? standardProperty<C, V>(\n            options,\n            protoOrTarget as\n              | ClassAccessorDecoratorTarget<C, V>\n              | ((value: V) => void),\n            nameOrContext as StandardPropertyContext<C, V>\n          )\n        : legacyProperty(\n            options,\n            protoOrTarget as Object,\n            nameOrContext as PropertyKey\n          )\n    ) as PropertyDecorator;\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {property} from './property.js';\n\nexport interface StateDeclaration<Type = unknown> {\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n}\n\n/**\n * @deprecated use StateDeclaration\n */\nexport type InternalPropertyDeclaration<Type = unknown> =\n  StateDeclaration<Type>;\n\n/**\n * Declares a private or protected reactive property that still triggers\n * updates to the element when it changes. It does not reflect from the\n * corresponding attribute.\n *\n * Properties declared this way must not be used from HTML or HTML templating\n * systems, they're solely for properties internal to the element. These\n * properties may be renamed by optimization tools like closure compiler.\n * @category Decorator\n */\nexport function state(options?: StateDeclaration) {\n  return property({\n    ...options,\n    // Add both `state` and `attribute` because we found a third party\n    // controller that is keying off of PropertyOptions.state to determine\n    // whether a field is a private internal property or not.\n    state: true,\n    attribute: false,\n  });\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport type {ReactiveElement} from '../reactive-element.js';\nimport type {Interface} from './base.js';\n\nexport type EventOptionsDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: PropertyKey\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  <C, V extends (this: C, ...args: any) => any>(\n    value: V,\n    _context: ClassMethodDecoratorContext<C, V>\n  ): void;\n};\n\n/**\n * Adds event listener options to a method used as an event listener in a\n * lit-html template.\n *\n * @param options An object that specifies event listener options as accepted by\n * `EventTarget#addEventListener` and `EventTarget#removeEventListener`.\n *\n * Current browsers support the `capture`, `passive`, and `once` options. See:\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Parameters\n *\n * ```ts\n * class MyElement {\n *   clicked = false;\n *\n *   render() {\n *     return html`\n *       <div @click=${this._onClick}>\n *         <button></button>\n *       </div>\n *     `;\n *   }\n *\n *   @eventOptions({capture: true})\n *   _onClick(e) {\n *     this.clicked = true;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function eventOptions(\n  options: AddEventListenerOptions\n): EventOptionsDecorator {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (<C, V extends (this: C, ...args: any) => any>(\n    protoOrValue: V,\n    nameOrContext: PropertyKey | ClassMethodDecoratorContext<C, V>\n  ) => {\n    const method =\n      typeof protoOrValue === 'function'\n        ? protoOrValue\n        : protoOrValue[nameOrContext as keyof ReactiveElement];\n    Object.assign(method, options);\n  }) as EventOptionsDecorator;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Generates a public interface type that removes private and protected fields.\n * This allows accepting otherwise incompatible versions of the type (e.g. from\n * multiple copies of the same package in `node_modules`).\n */\nexport type Interface<T> = {\n  [K in keyof T]: T[K];\n};\n\nexport type Constructor<T> = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  new (...args: any[]): T;\n};\n\n/**\n * Wraps up a few best practices when returning a property descriptor from a\n * decorator.\n *\n * Marks the defined property as configurable, and enumerable, and handles\n * the case where we have a busted Reflect.decorate zombiefill (e.g. in Angular\n * apps).\n *\n * @internal\n */\nexport const desc = (\n  obj: object,\n  name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>,\n  descriptor: PropertyDescriptor\n) => {\n  // For backwards compatibility, we keep them configurable and enumerable.\n  descriptor.configurable = true;\n  descriptor.enumerable = true;\n  if (\n    // We check for Reflect.decorate each time, in case the zombiefill\n    // is applied via lazy loading some Angular code.\n    (Reflect as typeof Reflect & {decorate?: unknown}).decorate &&\n    typeof name !== 'object'\n  ) {\n    // If we're called as a legacy decorator, and Reflect.decorate is present\n    // then we have no guarantees that the returned descriptor will be\n    // defined on the class, so we must apply it directly ourselves.\n\n    Object.defineProperty(obj, name, descriptor);\n  }\n  return descriptor;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  globalThis.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += code\n      ? ` See https://lit.dev/msg/${code} for more information.`\n      : '';\n    if (\n      !globalThis.litIssuedWarnings!.has(warning) &&\n      !globalThis.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      globalThis.litIssuedWarnings!.add(warning);\n    }\n  };\n}\n\nexport type QueryDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: PropertyKey,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Element | null>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function query(selector: string, cache?: boolean): QueryDecorator {\n  return (<C extends Interface<ReactiveElement>, V extends Element | null>(\n    protoOrTarget: ClassAccessorDecoratorTarget<C, V>,\n    nameOrContext: PropertyKey | ClassAccessorDecoratorContext<C, V>,\n    descriptor?: PropertyDescriptor\n  ) => {\n    const doQuery = (el: Interface<ReactiveElement>): V => {\n      const result = (el.renderRoot?.querySelector(selector) ?? null) as V;\n      if (DEV_MODE && result === null && cache && !el.hasUpdated) {\n        const name =\n          typeof nameOrContext === 'object'\n            ? nameOrContext.name\n            : nameOrContext;\n        issueWarning(\n          '',\n          `@query'd field ${JSON.stringify(String(name))} with the 'cache' ` +\n            `flag set for selector '${selector}' has been accessed before ` +\n            `the first update and returned null. This is expected if the ` +\n            `renderRoot tree has not been provided beforehand (e.g. via ` +\n            `Declarative Shadow DOM). Therefore the value hasn't been cached.`\n        );\n      }\n      // TODO: if we want to allow users to assert that the query will never\n      // return null, we need a new option and to throw here if the result\n      // is null.\n      return result;\n    };\n    if (cache) {\n      // Accessors to wrap from either:\n      //   1. The decorator target, in the case of standard decorators\n      //   2. The property descriptor, in the case of experimental decorators\n      //      on auto-accessors.\n      //   3. Functions that access our own cache-key property on the instance,\n      //      in the case of experimental decorators on fields.\n      const {get, set} =\n        typeof nameOrContext === 'object'\n          ? protoOrTarget\n          : descriptor ??\n            (() => {\n              const key = DEV_MODE\n                ? Symbol(`${String(nameOrContext)} (@query() cache)`)\n                : Symbol();\n              type WithCache = ReactiveElement & {\n                [key: symbol]: Element | null;\n              };\n              return {\n                get() {\n                  return (this as WithCache)[key];\n                },\n                set(v) {\n                  (this as WithCache)[key] = v;\n                },\n              };\n            })();\n      return desc(protoOrTarget, nameOrContext, {\n        get(this: ReactiveElement): V {\n          let result: V = get!.call(this);\n          if (result === undefined) {\n            result = doQuery(this);\n            if (result !== null || this.hasUpdated) {\n              set!.call(this, result);\n            }\n          }\n          return result;\n        },\n      });\n    } else {\n      // This object works as the return type for both standard and\n      // experimental decorators.\n      return desc(protoOrTarget, nameOrContext, {\n        get(this: ReactiveElement) {\n          return doQuery(this);\n        },\n      });\n    }\n  }) as QueryDecorator;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\nexport type QueryAllDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: <PERSON><PERSON><PERSON>,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends NodeList>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n// Shared fragment used to generate empty NodeLists when a render root is\n// undefined\nlet fragment: DocumentFragment;\n\n/**\n * A property decorator that converts a class property into a getter\n * that executes a querySelectorAll on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See:\n * https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelectorAll\n *\n * ```ts\n * class MyElement {\n *   @queryAll('div')\n *   divs: NodeListOf<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function queryAll(selector: string): QueryAllDecorator {\n  return ((\n    obj: object,\n    name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>\n  ) => {\n    return desc(obj, name, {\n      get(this: ReactiveElement) {\n        const container =\n          this.renderRoot ?? (fragment ??= document.createDocumentFragment());\n        return container.querySelectorAll(selector);\n      },\n    });\n  }) as QueryAllDecorator;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\nexport type QueryAsyncDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: <PERSON><PERSON><PERSON>,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Promise<Element | null>>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n// Note, in the future, we may extend this decorator to support the use case\n// where the queried element may need to do work to become ready to interact\n// with (e.g. load some implementation code). If so, we might elect to\n// add a second argument defining a function that can be run to make the\n// queried element loaded/updated/ready.\n/**\n * A property decorator that converts a class property into a getter that\n * returns a promise that resolves to the result of a querySelector on the\n * element's renderRoot done after the element's `updateComplete` promise\n * resolves. When the queried property may change with element state, this\n * decorator can be used instead of requiring users to await the\n * `updateComplete` before accessing the property.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @queryAsync('#first')\n *   first: Promise<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n *\n * // external usage\n * async doSomethingWithFirst() {\n *  (await aMyElement.first).doSomething();\n * }\n * ```\n * @category Decorator\n */\nexport function queryAsync(selector: string) {\n  return ((\n    obj: object,\n    name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>\n  ) => {\n    return desc(obj, name, {\n      async get(this: ReactiveElement) {\n        await this.updateComplete;\n        return this.renderRoot?.querySelector(selector) ?? null;\n      },\n    });\n  }) as QueryAsyncDecorator;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport type {ReactiveElement} from '../reactive-element.js';\nimport type {QueryAssignedNodesOptions} from './query-assigned-nodes.js';\nimport {desc, type Interface} from './base.js';\n\nexport type QueryAssignedElementsDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: <PERSON><PERSON><PERSON>,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Array<Element>>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n/**\n * Options for the {@linkcode queryAssignedElements} decorator. Extends the\n * options that can be passed into\n * [HTMLSlotElement.assignedElements](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n */\nexport interface QueryAssignedElementsOptions\n  extends QueryAssignedNodesOptions {\n  /**\n   * CSS selector used to filter the elements returned. For example, a selector\n   * of `\".item\"` will only include elements with the `item` class.\n   */\n  selector?: string;\n}\n\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedElements` of the given `slot`. Provides a declarative\n * way to use\n * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n *\n * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedElements({ slot: 'list' })\n *   listItems!: Array<HTMLElement>;\n *   @queryAssignedElements()\n *   unnamedSlotEls!: Array<HTMLElement>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *       <slot></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note, the type of this property should be annotated as `Array<HTMLElement>`.\n *\n * @category Decorator\n */\nexport function queryAssignedElements(\n  options?: QueryAssignedElementsOptions\n): QueryAssignedElementsDecorator {\n  return (<V extends Array<Element>>(\n    obj: object,\n    name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>\n  ) => {\n    const {slot, selector} = options ?? {};\n    const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n    return desc(obj, name, {\n      get(this: ReactiveElement): V {\n        const slotEl =\n          this.renderRoot?.querySelector<HTMLSlotElement>(slotSelector);\n        const elements = slotEl?.assignedElements(options) ?? [];\n        return (\n          selector === undefined\n            ? elements\n            : elements.filter((node) => node.matches(selector))\n        ) as V;\n      },\n    });\n  }) as QueryAssignedElementsDecorator;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\n/**\n * Options for the {@linkcode queryAssignedNodes} decorator. Extends the options\n * that can be passed into [HTMLSlotElement.assignedNodes](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedNodes).\n */\nexport interface QueryAssignedNodesOptions extends AssignedNodesOptions {\n  /**\n   * Name of the slot to query. Leave empty for the default slot.\n   */\n  slot?: string;\n}\n\nexport type QueryAssignedNodesDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: <PERSON><PERSON><PERSON>,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Array<Node>>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedNodes` of the given `slot`.\n *\n * Can be passed an optional {@linkcode QueryAssignedNodesOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedNodes({slot: 'list', flatten: true})\n *   listItems!: Array<Node>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note the type of this property should be annotated as `Array<Node>`. Use the\n * queryAssignedElements decorator to list only elements, and optionally filter\n * the element list using a CSS selector.\n *\n * @category Decorator\n */\nexport function queryAssignedNodes(\n  options?: QueryAssignedNodesOptions\n): QueryAssignedNodesDecorator {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (<V extends Array<Node>>(\n    obj: object,\n    name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>\n  ) => {\n    const {slot} = options ?? {};\n    const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n    return desc(obj, name, {\n      get(this: ReactiveElement): V {\n        const slotEl =\n          this.renderRoot?.querySelector<HTMLSlotElement>(slotSelector);\n        return (slotEl?.assignedNodes(options) ?? []) as unknown as V;\n      },\n    });\n  }) as QueryAssignedNodesDecorator;\n}\n"], "mappings": ";;;;;;AA6CO,IAAM,gBACX,CAAC,YACD,CACE,eACA,YACE;AACF,MAAI,YAAY,QAAW;AACzB,YAAQ,eAAe,MAAK;AAC1B,qBAAe,OACb,SACA,aAAyC;IAE7C,CAAC;EACH,OAAO;AACL,mBAAe,OAAO,SAAS,aAAyC;EAC1E;AACF;;;ACxCF,IAAM,WAAW;AAEjB,IAAI;AAEJ,IAAI,UAAU;AAGZ,aAAW,sBAAsB,oBAAI,IAAG;AAOxC,iBAAe,CAAC,MAAc,YAAmB;AAC/C,eAAW,4BAA4B,IAAI;AAC3C,QACE,CAAC,WAAW,kBAAmB,IAAI,OAAO,KAC1C,CAAC,WAAW,kBAAmB,IAAI,IAAI,GACvC;AACA,cAAQ,KAAK,OAAO;AACpB,iBAAW,kBAAmB,IAAI,OAAO;IAC3C;EACF;AACF;AA2BA,IAAM,iBAAiB,CACrB,SACA,OACA,SACE;AACF,QAAM,iBAAiB,MAAM,eAAe,IAAI;AAC/C,QAAM,YAAuC,eAAe,MAAM,OAAO;AAM1E,SAAO,iBACH,OAAO,yBAAyB,OAAO,IAAI,IAC3C;AACN;AAKA,IAAM,6BAAkD;EACtD,WAAW;EACX,MAAM;EACN,WAAW;EACX,SAAS;EACT,YAAY;;AAaP,IAAM,mBAAmB,CAC9B,UAA+B,4BAC/B,QACA,YACsE;AACtE,QAAM,EAAC,MAAM,SAAQ,IAAI;AAEzB,MAAI,YAAY,YAAY,MAAM;AAChC,iBACE,0BACA,aAAa,MAAM,oMAGa;EAEpC;AAGA,MAAI,aAAa,WAAW,oBAAoB,IAAI,QAAQ;AAC5D,MAAI,eAAe,QAAW;AAC5B,eAAW,oBAAoB,IAAI,UAAW,aAAa,oBAAI,IAAG,CAAG;EACvE;AACA,MAAI,SAAS,UAAU;AACrB,cAAU,OAAO,OAAO,OAAO;AAC/B,YAAQ,UAAU;EACpB;AACA,aAAW,IAAI,QAAQ,MAAM,OAAO;AAEpC,MAAI,SAAS,YAAY;AAIvB,UAAM,EAAC,KAAI,IAAI;AACf,WAAO;MACL,IAA2B,GAAI;AAC7B,cAAM,WACJ,OACA,IAAI,KAAK,IAAoB;AAC9B,eAA8C,IAAI,KACjD,MACA,CAAC;AAEH,aAAK,cAAc,MAAM,UAAU,OAAO;MAC5C;MACA,KAA4B,GAAI;AAC9B,YAAI,MAAM,QAAW;AACnB,eAAK,iBAAiB,MAAM,QAAW,SAAS,CAAC;QACnD;AACA,eAAO;MACT;;EAEJ,WAAW,SAAS,UAAU;AAC5B,UAAM,EAAC,KAAI,IAAI;AACf,WAAO,SAAiC,OAAQ;AAC9C,YAAM,WAAW,KAAK,IAA6B;AAClD,aAA8B,KAAK,MAAM,KAAK;AAC/C,WAAK,cAAc,MAAM,UAAU,OAAO;IAC5C;EACF;AACA,QAAM,IAAI,MAAM,mCAAmC,IAAI,EAAE;AAC3D;AAkCM,SAAU,SAAS,SAA6B;AACpD,SAAO,CACL,eAIA,kBAKO;AACP,WACE,OAAO,kBAAkB,WACrB,iBACE,SACA,eAGA,aAA8C,IAEhD,eACE,SACA,eACA,aAA4B;EAGtC;AACF;;;AChMM,SAAU,MAAM,SAA0B;AAC9C,SAAO,SAAS;IACd,GAAG;;;;IAIH,OAAO;IACP,WAAW;GACZ;AACH;;;ACcM,SAAU,aACd,SAAgC;AAGhC,SAAQ,CACN,cACA,kBACE;AACF,UAAM,SACJ,OAAO,iBAAiB,aACpB,eACA,aAAa,aAAsC;AACzD,WAAO,OAAO,QAAQ,OAAO;EAC/B;AACF;;;AC/CO,IAAM,OAAO,CAClB,KACA,MACA,eACE;AAEF,aAAW,eAAe;AAC1B,aAAW,aAAa;AACxB;;;IAGG,QAAkD,YACnD,OAAO,SAAS;IAChB;AAKA,WAAO,eAAe,KAAK,MAAM,UAAU;EAC7C;AACA,SAAO;AACT;;;ACpCA,IAAMA,YAAW;AAEjB,IAAIC;AAEJ,IAAID,WAAU;AAGZ,aAAW,sBAAsB,oBAAI,IAAG;AAOxC,EAAAC,gBAAe,CAAC,MAAc,YAAmB;AAC/C,eAAW,OACP,4BAA4B,IAAI,2BAChC;AACJ,QACE,CAAC,WAAW,kBAAmB,IAAI,OAAO,KAC1C,CAAC,WAAW,kBAAmB,IAAI,IAAI,GACvC;AACA,cAAQ,KAAK,OAAO;AACpB,iBAAW,kBAAmB,IAAI,OAAO;IAC3C;EACF;AACF;AA4CM,SAAU,MAAM,UAAkB,OAAe;AACrD,SAAQ,CACN,eACA,eACA,eACE;AACF,UAAM,UAAU,CAAC,OAAqC;AACpD,YAAM,SAAU,GAAG,YAAY,cAAc,QAAQ,KAAK;AAC1D,UAAID,aAAY,WAAW,QAAQ,SAAS,CAAC,GAAG,YAAY;AAC1D,cAAM,OACJ,OAAO,kBAAkB,WACrB,cAAc,OACd;AACN,QAAAC,cACE,IACA,kBAAkB,KAAK,UAAU,OAAO,IAAI,CAAC,CAAC,4CAClB,QAAQ,oNAGgC;MAExE;AAIA,aAAO;IACT;AACA,QAAI,OAAO;AAOT,YAAM,EAAC,KAAK,IAAG,IACb,OAAO,kBAAkB,WACrB,gBACA,eACC,MAAK;AACJ,cAAM,MAAMD,YACR,OAAO,GAAG,OAAO,aAAa,CAAC,mBAAmB,IAClD,OAAM;AAIV,eAAO;UACL,MAAG;AACD,mBAAQ,KAAmB,GAAG;UAChC;UACA,IAAI,GAAC;AACF,iBAAmB,GAAG,IAAI;UAC7B;;MAEJ,GAAE;AACR,aAAO,KAAK,eAAe,eAAe;QACxC,MAAG;AACD,cAAI,SAAY,IAAK,KAAK,IAAI;AAC9B,cAAI,WAAW,QAAW;AACxB,qBAAS,QAAQ,IAAI;AACrB,gBAAI,WAAW,QAAQ,KAAK,YAAY;AACtC,kBAAK,KAAK,MAAM,MAAM;YACxB;UACF;AACA,iBAAO;QACT;OACD;IACH,OAAO;AAGL,aAAO,KAAK,eAAe,eAAe;QACxC,MAAG;AACD,iBAAO,QAAQ,IAAI;QACrB;OACD;IACH;EACF;AACF;;;AC/HA,IAAI;AA0BE,SAAU,SAAS,UAAgB;AACvC,SAAQ,CACN,KACA,SACE;AACF,WAAO,KAAK,KAAK,MAAM;MACrB,MAAG;AACD,cAAM,YACJ,KAAK,eAAe,aAAa,SAAS,uBAAsB;AAClE,eAAO,UAAU,iBAAiB,QAAQ;MAC5C;KACD;EACH;AACF;;;ACHM,SAAU,WAAW,UAAgB;AACzC,SAAQ,CACN,KACA,SACE;AACF,WAAO,KAAK,KAAK,MAAM;MACrB,MAAM,MAAG;AACP,cAAM,KAAK;AACX,eAAO,KAAK,YAAY,cAAc,QAAQ,KAAK;MACrD;KACD;EACH;AACF;;;ACLM,SAAU,sBACd,SAAsC;AAEtC,SAAQ,CACN,KACA,SACE;AACF,UAAM,EAAC,MAAM,SAAQ,IAAI,WAAW,CAAA;AACpC,UAAM,eAAe,OAAO,OAAO,SAAS,IAAI,MAAM,cAAc;AACpE,WAAO,KAAK,KAAK,MAAM;MACrB,MAAG;AACD,cAAM,SACJ,KAAK,YAAY,cAA+B,YAAY;AAC9D,cAAM,WAAW,QAAQ,iBAAiB,OAAO,KAAK,CAAA;AACtD,eACE,aAAa,SACT,WACA,SAAS,OAAO,CAAC,SAAS,KAAK,QAAQ,QAAQ,CAAC;MAExD;KACD;EACH;AACF;;;AC9BM,SAAU,mBACd,SAAmC;AAGnC,SAAQ,CACN,KACA,SACE;AACF,UAAM,EAAC,KAAI,IAAI,WAAW,CAAA;AAC1B,UAAM,eAAe,OAAO,OAAO,SAAS,IAAI,MAAM,cAAc;AACpE,WAAO,KAAK,KAAK,MAAM;MACrB,MAAG;AACD,cAAM,SACJ,KAAK,YAAY,cAA+B,YAAY;AAC9D,eAAQ,QAAQ,cAAc,OAAO,KAAK,CAAA;MAC5C;KACD;EACH;AACF;", "names": ["DEV_MODE", "issueWarning"]}