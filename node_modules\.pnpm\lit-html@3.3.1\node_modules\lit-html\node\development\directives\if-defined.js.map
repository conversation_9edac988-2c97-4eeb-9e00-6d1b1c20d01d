{"version": 3, "file": "if-defined.js", "sources": ["../../../src/directives/if-defined.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {nothing} from '../lit-html.js';\n\n/**\n * For AttributeParts, sets the attribute if the value is defined and removes\n * the attribute if the value is undefined.\n *\n * For other part types, this directive is a no-op.\n */\nexport const ifDefined = <T>(value: T) => value ?? nothing;\n"], "names": [], "mappings": ";;AAAA;;;;AAIG;AAIH;;;;;AAKG;AACI,MAAM,SAAS,GAAG,CAAI,KAAQ,KAAK,KAAK,IAAI;;;;"}