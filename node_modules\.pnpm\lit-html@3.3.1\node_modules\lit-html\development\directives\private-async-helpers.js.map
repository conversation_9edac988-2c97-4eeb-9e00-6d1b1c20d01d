{"version": 3, "file": "private-async-helpers.js", "sourceRoot": "", "sources": ["../../src/directives/private-async-helpers.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,+EAA+E;AAC/E,gFAAgF;AAChF,aAAa;AAEb;;;;;GAKG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,KAAK,EAC7B,QAA0B,EAC1B,QAAwC,EACxC,EAAE;IACF,IAAI,KAAK,EAAE,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,OAAO,aAAa;IAExB,YAAY,GAAM;QAChB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAClB,CAAC;IACD;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;IACxB,CAAC;IACD;;OAEG;IACH,SAAS,CAAC,GAAM;QACd,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAClB,CAAC;IACD;;OAEG;IACH,KAAK;QACH,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,MAAM;IAAnB;QACU,aAAQ,GAAmB,SAAS,CAAC;QACrC,aAAQ,GAAgB,SAAS,CAAC;IAwB5C,CAAC;IAvBC;;;;;;OAMG;IACH,GAAG;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IACD;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,KAAK,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC;IACxE,CAAC;IACD;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;IAC5C,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// Note, this module is not included in package exports so that it's private to\n// our first-party directives. If it ends up being useful, we can open it up and\n// export it.\n\n/**\n * Helper to iterate an AsyncIterable in its own closure.\n * @param iterable The iterable to iterate\n * @param callback The callback to call for each value. If the callback returns\n * `false`, the loop will be broken.\n */\nexport const forAwaitOf = async <T>(\n  iterable: AsyncIterable<T>,\n  callback: (value: T) => Promise<boolean>\n) => {\n  for await (const v of iterable) {\n    if ((await callback(v)) === false) {\n      return;\n    }\n  }\n};\n\n/**\n * Holds a reference to an instance that can be disconnected and reconnected,\n * so that a closure over the ref (e.g. in a then function to a promise) does\n * not strongly hold a ref to the instance. Approximates a WeakRef but must\n * be manually connected & disconnected to the backing instance.\n */\nexport class PseudoWeakRef<T> {\n  private _ref?: T;\n  constructor(ref: T) {\n    this._ref = ref;\n  }\n  /**\n   * Disassociates the ref with the backing instance.\n   */\n  disconnect() {\n    this._ref = undefined;\n  }\n  /**\n   * Reassociates the ref with the backing instance.\n   */\n  reconnect(ref: T) {\n    this._ref = ref;\n  }\n  /**\n   * Retrieves the backing instance (will be undefined when disconnected)\n   */\n  deref() {\n    return this._ref;\n  }\n}\n\n/**\n * A helper to pause and resume waiting on a condition in an async function\n */\nexport class Pauser {\n  private _promise?: Promise<void> = undefined;\n  private _resolve?: () => void = undefined;\n  /**\n   * When paused, returns a promise to be awaited; when unpaused, returns\n   * undefined. Note that in the microtask between the pauser being resumed\n   * an await of this promise resolving, the pauser could be paused again,\n   * hence callers should check the promise in a loop when awaiting.\n   * @returns A promise to be awaited when paused or undefined\n   */\n  get() {\n    return this._promise;\n  }\n  /**\n   * Creates a promise to be awaited\n   */\n  pause() {\n    this._promise ??= new Promise((resolve) => (this._resolve = resolve));\n  }\n  /**\n   * Resolves the promise which may be awaited\n   */\n  resume() {\n    this._resolve?.();\n    this._promise = this._resolve = undefined;\n  }\n}\n"]}