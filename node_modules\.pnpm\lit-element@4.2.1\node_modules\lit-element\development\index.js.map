{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,cAAc,uBAAuB,CAAC;AACtC,cAAc,UAAU,CAAC;AACzB,cAAc,kBAAkB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nexport * from '@lit/reactive-element';\nexport * from 'lit-html';\nexport * from './lit-element.js';\n"]}