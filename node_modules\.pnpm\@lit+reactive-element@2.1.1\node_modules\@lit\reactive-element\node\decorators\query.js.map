{"version": 3, "file": "query.js", "sources": ["../../src/decorators/query.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  globalThis.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += code\n      ? ` See https://lit.dev/msg/${code} for more information.`\n      : '';\n    if (\n      !globalThis.litIssuedWarnings!.has(warning) &&\n      !globalThis.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      globalThis.litIssuedWarnings!.add(warning);\n    }\n  };\n}\n\nexport type QueryDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: PropertyKey,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Element | null>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function query(selector: string, cache?: boolean): QueryDecorator {\n  return (<C extends Interface<ReactiveElement>, V extends Element | null>(\n    protoOrTarget: ClassAccessorDecoratorTarget<C, V>,\n    nameOrContext: PropertyKey | ClassAccessorDecoratorContext<C, V>,\n    descriptor?: PropertyDescriptor\n  ) => {\n    const doQuery = (el: Interface<ReactiveElement>): V => {\n      const result = (el.renderRoot?.querySelector(selector) ?? null) as V;\n      if (DEV_MODE && result === null && cache && !el.hasUpdated) {\n        const name =\n          typeof nameOrContext === 'object'\n            ? nameOrContext.name\n            : nameOrContext;\n        issueWarning(\n          '',\n          `@query'd field ${JSON.stringify(String(name))} with the 'cache' ` +\n            `flag set for selector '${selector}' has been accessed before ` +\n            `the first update and returned null. This is expected if the ` +\n            `renderRoot tree has not been provided beforehand (e.g. via ` +\n            `Declarative Shadow DOM). Therefore the value hasn't been cached.`\n        );\n      }\n      // TODO: if we want to allow users to assert that the query will never\n      // return null, we need a new option and to throw here if the result\n      // is null.\n      return result;\n    };\n    if (cache) {\n      // Accessors to wrap from either:\n      //   1. The decorator target, in the case of standard decorators\n      //   2. The property descriptor, in the case of experimental decorators\n      //      on auto-accessors.\n      //   3. Functions that access our own cache-key property on the instance,\n      //      in the case of experimental decorators on fields.\n      const {get, set} =\n        typeof nameOrContext === 'object'\n          ? protoOrTarget\n          : descriptor ??\n            (() => {\n              const key = DEV_MODE\n                ? Symbol(`${String(nameOrContext)} (@query() cache)`)\n                : Symbol();\n              type WithCache = ReactiveElement & {\n                [key: symbol]: Element | null;\n              };\n              return {\n                get() {\n                  return (this as WithCache)[key];\n                },\n                set(v) {\n                  (this as WithCache)[key] = v;\n                },\n              };\n            })();\n      return desc(protoOrTarget, nameOrContext, {\n        get(this: ReactiveElement): V {\n          let result: V = get!.call(this);\n          if (result === undefined) {\n            result = doQuery(this);\n            if (result !== null || this.hasUpdated) {\n              set!.call(this, result);\n            }\n          }\n          return result;\n        },\n      });\n    } else {\n      // This object works as the return type for both standard and\n      // experimental decorators.\n      return desc(protoOrTarget, nameOrContext, {\n        get(this: ReactiveElement) {\n          return doQuery(this);\n        },\n      });\n    }\n  }) as QueryDecorator;\n}\n"], "names": ["query", "selector", "cache", "protoOrTarget", "nameOrContext", "descriptor", "<PERSON><PERSON><PERSON><PERSON>", "el", "renderRoot", "querySelector", "get", "set", "key", "Symbol", "this", "v", "desc", "result", "call", "undefined", "hasUpdated"], "mappings": ";;;;;GAqFgB,SAAAA,EAAMC,EAAkBC,GACtC,OACEC,EACAC,EACAC,KAEA,MAAMC,EAAWC,GACCA,EAAGC,YAAYC,cAAcR,IAAa,KAoB5D,GAAIC,EAAO,CAOT,MAAMQ,IAACA,EAAGC,IAAEA,GACe,iBAAlBP,EACHD,EACAE,GACA,MACE,MAAMO,EAEFC,SAIJ,MAAO,CACL,GAAAH,GACE,OAAQI,KAAmBF,EAC5B,EACD,GAAAD,CAAII,GACDD,KAAmBF,GAAOG,CAC5B,EAEJ,EAfD,GAgBN,OAAOC,EAAKb,EAAeC,EAAe,CACxC,GAAAM,GACE,IAAIO,EAAYP,EAAKQ,KAAKJ,MAO1B,YANeK,IAAXF,IACFA,EAASX,EAAQQ,OACF,OAAXG,GAAmBH,KAAKM,aAC1BT,EAAKO,KAAKJ,KAAMG,IAGbA,CACR,GAEJ,CAGC,OAAOD,EAAKb,EAAeC,EAAe,CACxC,GAAAM,GACE,OAAOJ,EAAQQ,KAChB,GAGN,CACH"}