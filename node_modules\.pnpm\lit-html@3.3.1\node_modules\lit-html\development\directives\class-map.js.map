{"version": 3, "file": "class-map.js", "sourceRoot": "", "sources": ["../../src/directives/class-map.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAgB,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AACvD,OAAO,EACL,SAAS,EACT,SAAS,EAGT,QAAQ,GACT,MAAM,iBAAiB,CAAC;AASzB,MAAM,iBAAkB,SAAQ,SAAS;IAQvC,YAAY,QAAkB;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IACE,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,SAAS;YACpC,QAAQ,CAAC,IAAI,KAAK,OAAO;YACxB,QAAQ,CAAC,OAAO,EAAE,MAAiB,GAAG,CAAC,EACxC,CAAC;YACD,MAAM,IAAI,KAAK,CACb,yDAAyD;gBACvD,6CAA6C,CAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,MAAM,CAAC,SAAoB;QACzB,sDAAsD;QACtD,OAAO,CACL,GAAG;YACH,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;iBACnB,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;iBAC/B,IAAI,CAAC,GAAG,CAAC;YACZ,GAAG,CACJ,CAAC;IACJ,CAAC;IAEQ,MAAM,CAAC,IAAmB,EAAE,CAAC,SAAS,CAA4B;QACzE,+CAA+C;QAC/C,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAC3B,IAAI,CAAC,OAAO;qBACT,IAAI,CAAC,GAAG,CAAC;qBACT,KAAK,CAAC,IAAI,CAAC;qBACX,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAC3B,CAAC;YACJ,CAAC;YACD,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;gBAC7B,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAChC,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAEzC,0CAA0C;QAC1C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzC,IAAI,CAAC,CAAC,IAAI,IAAI,SAAS,CAAC,EAAE,CAAC;gBACzB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACvB,IAAI,CAAC,gBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAED,sDAAsD;QACtD,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC7B,sEAAsE;YACtE,6CAA6C;YAC7C,MAAM,KAAK,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAChC,IACE,KAAK,KAAK,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC;gBACzC,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,EAC/B,CAAC;gBACD,IAAI,KAAK,EAAE,CAAC;oBACV,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBACpB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAClC,CAAC;qBAAM,CAAC;oBACN,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of class names to truthy values.\n */\nexport interface ClassInfo {\n  readonly [name: string]: string | boolean | number;\n}\n\nclass ClassMapDirective extends Directive {\n  /**\n   * Stores the ClassInfo object applied to a given AttributePart.\n   * Used to unset existing values when a new ClassInfo object is applied.\n   */\n  private _previousClasses?: Set<string>;\n  private _staticClasses?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'class' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        '`classMap()` can only be used in the `class` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(classInfo: ClassInfo) {\n    // Add spaces to ensure separation from static classes\n    return (\n      ' ' +\n      Object.keys(classInfo)\n        .filter((key) => classInfo[key])\n        .join(' ') +\n      ' '\n    );\n  }\n\n  override update(part: AttributePart, [classInfo]: DirectiveParameters<this>) {\n    // Remember dynamic classes on the first render\n    if (this._previousClasses === undefined) {\n      this._previousClasses = new Set();\n      if (part.strings !== undefined) {\n        this._staticClasses = new Set(\n          part.strings\n            .join(' ')\n            .split(/\\s/)\n            .filter((s) => s !== '')\n        );\n      }\n      for (const name in classInfo) {\n        if (classInfo[name] && !this._staticClasses?.has(name)) {\n          this._previousClasses.add(name);\n        }\n      }\n      return this.render(classInfo);\n    }\n\n    const classList = part.element.classList;\n\n    // Remove old classes that no longer apply\n    for (const name of this._previousClasses) {\n      if (!(name in classInfo)) {\n        classList.remove(name);\n        this._previousClasses!.delete(name);\n      }\n    }\n\n    // Add or remove classes based on their classMap value\n    for (const name in classInfo) {\n      // We explicitly want a loose truthy check of `value` because it seems\n      // more convenient that '' and 0 are skipped.\n      const value = !!classInfo[name];\n      if (\n        value !== this._previousClasses.has(name) &&\n        !this._staticClasses?.has(name)\n      ) {\n        if (value) {\n          classList.add(name);\n          this._previousClasses.add(name);\n        } else {\n          classList.remove(name);\n          this._previousClasses.delete(name);\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies dynamic CSS classes.\n *\n * This must be used in the `class` attribute and must be the only part used in\n * the attribute. It takes each property in the `classInfo` argument and adds\n * the property name to the element's `classList` if the property value is\n * truthy; if the property value is falsy, the property name is removed from\n * the element's `class`.\n *\n * For example `{foo: bar}` applies the class `foo` if the value of `bar` is\n * truthy.\n *\n * @param classInfo\n */\nexport const classMap = directive(ClassMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {ClassMapDirective};\n"]}