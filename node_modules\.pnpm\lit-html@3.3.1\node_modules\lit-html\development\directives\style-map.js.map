{"version": 3, "file": "style-map.js", "sourceRoot": "", "sources": ["../../src/directives/style-map.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAgB,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AACvD,OAAO,EACL,SAAS,EACT,SAAS,EAGT,QAAQ,GACT,MAAM,iBAAiB,CAAC;AAazB,MAAM,SAAS,GAAG,WAAW,CAAC;AAC9B,iCAAiC;AACjC,MAAM,aAAa,GAAG,IAAI,GAAG,SAAS,CAAC;AACvC,mEAAmE;AACnE,MAAM,QAAQ,GAAG,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;AAE1C,MAAM,iBAAkB,SAAQ,SAAS;IAGvC,YAAY,QAAkB;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IACE,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,SAAS;YACpC,QAAQ,CAAC,IAAI,KAAK,OAAO;YACxB,QAAQ,CAAC,OAAO,EAAE,MAAiB,GAAG,CAAC,EACxC,CAAC;YACD,MAAM,IAAI,KAAK,CACb,iEAAiE;gBAC/D,6CAA6C,CAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,MAAM,CAAC,SAA8B;QACnC,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACnD,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC9B,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,6DAA6D;YAC7D,2CAA2C;YAC3C,6DAA6D;YAC7D,8CAA8C;YAC9C,8DAA8D;YAC9D,kEAAkE;YAClE,+CAA+C;YAC/C,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACvB,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,IAAI;qBACD,OAAO,CAAC,mCAAmC,EAAE,KAAK,CAAC;qBACnD,WAAW,EAAE,CAAC;YACrB,OAAO,KAAK,GAAG,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC;QACrC,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEQ,MAAM,CAAC,IAAmB,EAAE,CAAC,SAAS,CAA4B;QACzE,MAAM,EAAC,KAAK,EAAC,GAAG,IAAI,CAAC,OAAsB,CAAC;QAE5C,IAAI,IAAI,CAAC,wBAAwB,KAAK,SAAS,EAAE,CAAC;YAChD,IAAI,CAAC,wBAAwB,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAChC,CAAC;QAED,0DAA0D;QAC1D,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjD,wDAAwD;YACxD,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5B,IAAI,CAAC,wBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvB,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACN,8DAA8D;oBAC7D,KAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC9B,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACxC,MAAM,WAAW,GACf,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAC7D,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,WAAW,EAAE,CAAC;oBACtC,KAAK,CAAC,WAAW,CACf,IAAI,EACJ,WAAW;wBACT,CAAC,CAAE,KAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC;wBACtC,CAAC,CAAE,KAAgB,EACrB,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAC7B,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,8DAA8D;oBAC7D,KAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of CSS properties and values.\n *\n * The key should be either a valid CSS property name string, like\n * `'background-color'`, or a valid JavaScript camel case property name\n * for CSSStyleDeclaration like `backgroundColor`.\n */\nexport interface StyleInfo {\n  [name: string]: string | number | undefined | null;\n}\n\nconst important = 'important';\n// The leading space is important\nconst importantFlag = ' !' + important;\n// How many characters to remove from a value, as a negative number\nconst flagTrim = 0 - importantFlag.length;\n\nclass StyleMapDirective extends Directive {\n  private _previousStyleProperties?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'style' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        'The `styleMap` directive must be used in the `style` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(styleInfo: Readonly<StyleInfo>) {\n    return Object.keys(styleInfo).reduce((style, prop) => {\n      const value = styleInfo[prop];\n      if (value == null) {\n        return style;\n      }\n      // Convert property names from camel-case to dash-case, i.e.:\n      //  `backgroundColor` -> `background-color`\n      // Vendor-prefixed names need an extra `-` appended to front:\n      //  `webkitAppearance` -> `-webkit-appearance`\n      // Exception is any property name containing a dash, including\n      // custom properties; we assume these are already dash-cased i.e.:\n      //  `--my-button-color` --> `--my-button-color`\n      prop = prop.includes('-')\n        ? prop\n        : prop\n            .replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g, '-$&')\n            .toLowerCase();\n      return style + `${prop}:${value};`;\n    }, '');\n  }\n\n  override update(part: AttributePart, [styleInfo]: DirectiveParameters<this>) {\n    const {style} = part.element as HTMLElement;\n\n    if (this._previousStyleProperties === undefined) {\n      this._previousStyleProperties = new Set(Object.keys(styleInfo));\n      return this.render(styleInfo);\n    }\n\n    // Remove old properties that no longer exist in styleInfo\n    for (const name of this._previousStyleProperties) {\n      // If the name isn't in styleInfo or it's null/undefined\n      if (styleInfo[name] == null) {\n        this._previousStyleProperties!.delete(name);\n        if (name.includes('-')) {\n          style.removeProperty(name);\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          (style as any)[name] = null;\n        }\n      }\n    }\n\n    // Add or update properties\n    for (const name in styleInfo) {\n      const value = styleInfo[name];\n      if (value != null) {\n        this._previousStyleProperties.add(name);\n        const isImportant =\n          typeof value === 'string' && value.endsWith(importantFlag);\n        if (name.includes('-') || isImportant) {\n          style.setProperty(\n            name,\n            isImportant\n              ? (value as string).slice(0, flagTrim)\n              : (value as string),\n            isImportant ? important : ''\n          );\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          (style as any)[name] = value;\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies CSS properties to an element.\n *\n * `styleMap` can only be used in the `style` attribute and must be the only\n * expression in the attribute. It takes the property names in the\n * {@link StyleInfo styleInfo} object and adds the properties to the inline\n * style of the element.\n *\n * Property names with dashes (`-`) are assumed to be valid CSS\n * property names and set on the element's style object using `setProperty()`.\n * Names without dashes are assumed to be camelCased JavaScript property names\n * and set on the element's style object using property assignment, allowing the\n * style object to translate JavaScript-style names to CSS property names.\n *\n * For example `styleMap({backgroundColor: 'red', 'border-top': '5px', '--size':\n * '0'})` sets the `background-color`, `border-top` and `--size` properties.\n *\n * @param styleInfo\n * @see {@link https://lit.dev/docs/templates/directives/#stylemap styleMap code samples on Lit.dev}\n */\nexport const styleMap = directive(StyleMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {StyleMapDirective};\n"]}