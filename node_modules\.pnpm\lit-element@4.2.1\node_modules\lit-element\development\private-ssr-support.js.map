{"version": 3, "file": "private-ssr-support.js", "sourceRoot": "", "sources": ["../src/private-ssr-support.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,IAAI,IAAI,CAAC,EAAC,MAAM,kBAAkB,CAAC;AAE3C;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG;IAClB,mBAAmB,EAAE,CAAC,CAAC,qBAAqB;IAC5C,iBAAiB,EAAE,CAAC,CAAC,mBAAmB;CACzC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {_$LE as p} from './lit-element.js';\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LE object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * @private\n */\nexport const _$LE = {\n  attributeToProperty: p._$attributeToProperty,\n  changedProperties: p._$changedProperties,\n};\n"]}