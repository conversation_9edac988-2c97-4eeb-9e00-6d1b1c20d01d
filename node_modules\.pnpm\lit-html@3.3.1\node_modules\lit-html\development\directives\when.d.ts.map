{"version": 3, "file": "when.d.ts", "sourceRoot": "", "sources": ["../../src/directives/when.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,KAAK,KAAK,GAAG,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAEzD;;;;;;;;;;;;;;;;GAgBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC,GAAG,SAAS,EACpD,SAAS,EAAE,CAAC,EACZ,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EACrB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GACtB,CAAC,CAAC;AACL,wBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAC1B,SAAS,EAAE,CAAC,SAAS,KAAK,GAAG,KAAK,GAAG,CAAC,EACtC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EACrB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GACtB,CAAC,CAAC;AACL,wBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,SAAS,EACtC,SAAS,EAAE,CAAC,EACZ,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,EACrC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,GACtC,CAAC,SAAS,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC"}