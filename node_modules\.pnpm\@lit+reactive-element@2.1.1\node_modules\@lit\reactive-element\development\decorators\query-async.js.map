{"version": 3, "file": "query-async.js", "sourceRoot": "", "sources": ["../../src/decorators/query-async.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAUH,OAAO,EAAC,IAAI,EAAiB,MAAM,WAAW,CAAC;AAmB/C,4EAA4E;AAC5E,4EAA4E;AAC5E,sEAAsE;AACtE,wEAAwE;AACxE,wCAAwC;AACxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,MAAM,UAAU,UAAU,CAAC,QAAgB;IACzC,OAAO,CAAC,CACN,GAAW,EACX,IAAmE,EACnE,EAAE;QACF,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE;YACrB,KAAK,CAAC,GAAG;gBACP,MAAM,IAAI,CAAC,cAAc,CAAC;gBAC1B,OAAO,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;YAC1D,CAAC;SACF,CAAC,CAAC;IACL,CAAC,CAAwB,CAAC;AAC5B,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\nexport type QueryAsyncDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: <PERSON><PERSON><PERSON>,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Promise<Element | null>>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n// Note, in the future, we may extend this decorator to support the use case\n// where the queried element may need to do work to become ready to interact\n// with (e.g. load some implementation code). If so, we might elect to\n// add a second argument defining a function that can be run to make the\n// queried element loaded/updated/ready.\n/**\n * A property decorator that converts a class property into a getter that\n * returns a promise that resolves to the result of a querySelector on the\n * element's renderRoot done after the element's `updateComplete` promise\n * resolves. When the queried property may change with element state, this\n * decorator can be used instead of requiring users to await the\n * `updateComplete` before accessing the property.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @queryAsync('#first')\n *   first: Promise<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n *\n * // external usage\n * async doSomethingWithFirst() {\n *  (await aMyElement.first).doSomething();\n * }\n * ```\n * @category Decorator\n */\nexport function queryAsync(selector: string) {\n  return ((\n    obj: object,\n    name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>\n  ) => {\n    return desc(obj, name, {\n      async get(this: ReactiveElement) {\n        await this.updateComplete;\n        return this.renderRoot?.querySelector(selector) ?? null;\n      },\n    });\n  }) as QueryAsyncDecorator;\n}\n"]}