{"version": 3, "file": "events.js", "sourceRoot": "", "sources": ["../src/lib/events.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;AA2BH,MAAM,sBAAsB,GAAG,CAC7B,OAAsD,EACtD,EAAE,CAAC,CAAC,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,KAAK,CAAC,CAAC;AAE1E,eAAe;AACf,MAAM,IAAI,GAAG,CAAC,CAAC;AACf,MAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,MAAM,SAAS,GAAG,CAAC,CAAC;AACpB,MAAM,cAAc,GAAG,CAAC,CAAC;AAEzB,qCAAqC;AACrC,MAAM,WAAW;IAAjB;QACU,qBAAgB,GAAG,IAAI,GAAG,EAG/B,CAAC;QACI,4BAAuB,GAAG,IAAI,GAAG,EAGtC,CAAC;IAkON,CAAC;IA9NC,gBAAgB,CACd,IAAY,EACZ,QAAmD,EACnD,OAA2C;QAE3C,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YAChD,OAAO;QACT,CAAC;QACD,MAAM,iBAAiB,GAAG,sBAAsB,CAAC,OAAO,CAAC;YACvD,CAAC,CAAC,IAAI,CAAC,uBAAuB;YAC9B,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;QAC1B,IAAI,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACjC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;YAC3B,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,OAAO;QACT,CAAC;QAED,MAAM,iBAAiB,GACrB,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CACvD,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAClD,CAAC;QACF,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,iBAAiB,IAAI,EAAE,CAAC,CAAC;IACxD,CAAC;IACD,mBAAmB,CACjB,IAAY,EACZ,QAAmD,EACnD,OAAwC;QAExC,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YAChD,OAAO;QACT,CAAC;QACD,MAAM,iBAAiB,GAAG,sBAAsB,CAAC,OAAO,CAAC;YACvD,CAAC,CAAC,IAAI,CAAC,uBAAuB;YAC9B,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;QAC1B,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACjC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAChC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;gBACzB,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;IACH,CAAC;IACD,aAAa,CAAC,KAAY;QACxB,MAAM,YAAY,GAAkB,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACtC,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,MAAM,EAAE,CAAC;gBACd,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1B,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC;YACtC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,mEAAmE;YACnE,iEAAiE;YACjE,OAAO,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;gBACxC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1B,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC;YACtC,CAAC;QACH,CAAC;QAED,0EAA0E;QAC1E,oBAAoB;QACpB,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,wBAAwB,GAAG,KAAK,CAAC;QACrC,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,MAAM,GAAuB,IAAI,CAAC;QACtC,IAAI,SAAS,GAAuB,IAAI,CAAC;QACzC,IAAI,aAAa,GAAuB,IAAI,CAAC;QAC7C,MAAM,uBAAuB,GAAG,KAAK,CAAC,eAAe,CAAC;QACtD,MAAM,gCAAgC,GAAG,KAAK,CAAC,wBAAwB,CAAC;QACxE,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE;YAC7B,MAAM,EAAE;gBACN,GAAG;oBACD,OAAO,MAAM,IAAI,SAAS,CAAC;gBAC7B,CAAC;gBACD,GAAG,kBAAkB;aACtB;YACD,UAAU,EAAE;gBACV,GAAG;oBACD,OAAO,KAAK,CAAC,MAAM,CAAC;gBACtB,CAAC;gBACD,GAAG,kBAAkB;aACtB;YACD,aAAa,EAAE;gBACb,GAAG;oBACD,OAAO,aAAa,CAAC;gBACvB,CAAC;gBACD,GAAG,kBAAkB;aACtB;YACD,UAAU,EAAE;gBACV,GAAG;oBACD,OAAO,UAAU,CAAC;gBACpB,CAAC;gBACD,GAAG,kBAAkB;aACtB;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,GAAG,EAAE,CAAC,YAAY;gBACzB,GAAG,kBAAkB;aACtB;YACD,eAAe,EAAE;gBACf,KAAK,EAAE,GAAG,EAAE;oBACV,eAAe,GAAG,IAAI,CAAC;oBACvB,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtC,CAAC;gBACD,GAAG,kBAAkB;aACtB;YACD,wBAAwB,EAAE;gBACxB,KAAK,EAAE,GAAG,EAAE;oBACV,wBAAwB,GAAG,IAAI,CAAC;oBAChC,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/C,CAAC;gBACD,GAAG,kBAAkB;aACtB;SACF,CAAC,CAAC;QAEH,0EAA0E;QAC1E,qEAAqE;QACrE,aAAa;QACb,MAAM,mBAAmB,GAAG,CAC1B,QAA4C,EAC5C,OAAgC,EAChC,gBAGC,EACD,EAAE;YACF,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;gBACnC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC;iBAAM,IAAI,OAAO,QAAQ,EAAE,WAAW,KAAK,UAAU,EAAE,CAAC;gBACvD,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;YACD,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACjB,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;QACH,CAAC,CAAC;QACF,2EAA2E;QAC3E,0EAA0E;QAC1E,uEAAuE;QACvE,uEAAuE;QACvE,wCAAwC;QACxC,MAAM,cAAc,GAAG,GAAG,EAAE;YAC1B,aAAa,GAAG,IAAI,CAAC;YACrB,UAAU,GAAG,IAAI,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC;QACjC,CAAC,CAAC;QAEF,wEAAwE;QACxE,sEAAsE;QACtE,MAAM,gBAAgB,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC;QACxD,8EAA8E;QAC9E,4EAA4E;QAC5E,qFAAqF;QACrF,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QACvD,MAAM,QAAQ,GAAG,CAAC,YAA2B,EAAE,EAAE;YAC/C,4DAA4D;YAC5D,SAAS,GAAG,IAAI,CAAC;YACjB,OAAO,SAAS,CAAC,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnE,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;YAC/B,CAAC;QACH,CAAC,CAAC;QACF,KAAK,MAAM,WAAW,IAAI,gBAAgB,EAAE,CAAC;YAC3C,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,KAAK,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;gBAChE,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC1E,CAAC;YACD,aAAa,GAAG,WAAW,CAAC;YAC5B,UAAU,GAAG,WAAW,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC;YACxE,MAAM,qBAAqB,GAAG,WAAW,CAAC,uBAAuB,CAAC,GAAG,CACnE,KAAK,CAAC,IAAI,CACX,CAAC;YACF,IAAI,qBAAqB,EAAE,CAAC;gBAC1B,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,qBAAqB,EAAE,CAAC;oBACxD,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;oBAC9D,IAAI,wBAAwB,EAAE,CAAC;wBAC7B,kEAAkE;wBAClE,qDAAqD;wBACrD,OAAO,cAAc,EAAE,CAAC;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,eAAe,EAAE,CAAC;gBACpB,yDAAyD;gBACzD,uDAAuD;gBACvD,OAAO,cAAc,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC9D,SAAS,GAAG,IAAI,CAAC;QACjB,KAAK,MAAM,WAAW,IAAI,eAAe,EAAE,CAAC;YAC1C,IACE,CAAC,MAAM;gBACP,CAAC,CAAC,SAAS,IAAI,WAAW,KAAM,SAAyB,CAAC,MAAM,CAAC,EACjE,CAAC;gBACD,QAAQ,CACN,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CACnE,CAAC;YACJ,CAAC;YACD,aAAa,GAAG,WAAW,CAAC;YAC5B,UAAU,GAAG,WAAW,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC;YACvE,MAAM,qBAAqB,GAAG,WAAW,CAAC,gBAAgB,CAAC,GAAG,CAC5D,KAAK,CAAC,IAAI,CACX,CAAC;YACF,IAAI,qBAAqB,EAAE,CAAC;gBAC1B,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,qBAAqB,EAAE,CAAC;oBACxD,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;oBAC9D,IAAI,wBAAwB,EAAE,CAAC;wBAC7B,kEAAkE;wBAClE,qDAAqD;wBACrD,OAAO,cAAc,EAAE,CAAC;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,eAAe,EAAE,CAAC;gBACpB,yDAAyD;gBACzD,uDAAuD;gBACvD,OAAO,cAAc,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC;QACD,OAAO,cAAc,EAAE,CAAC;IAC1B,CAAC;CACF;AAED,MAAM,2BAA2B,GAC/B,WAAsD,CAAC;AACzD,OAAO,EACL,2BAA2B,IAAI,WAAW,EAC1C,2BAA2B,IAAI,eAAe,GAC/C,CAAC;AAMF,MAAM,kBAAkB,GAA4B,EAAC,SAAS,EAAE,IAAI,EAAC,CAAC;AACtE,kBAAkB,CAAC,UAAU,GAAG,IAAI,CAAC;AACrC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAElC,uFAAuF;AACvF,MAAM,SAAS,SAAG,MAAM,KAAK;QAmB3B,YAAY,IAAY,EAAE,UAAqB,EAAE;YAlBjD,4BAAc,KAAK,EAAC;YACpB,yBAAW,KAAK,EAAC;YACjB,0BAAY,KAAK,EAAC;YAClB,kCAAoB,KAAK,EAAC;YAC1B,2BAAa,IAAI,CAAC,GAAG,EAAE,EAAC;YACxB,oCAAsB,KAAK,EAAC;YAC5B,8BAAc;YACd,gCAAuC;YACvC,2CAA4B;YACnB,SAAI,GAAG,IAAI,CAAC;YACZ,oBAAe,GAAG,eAAe,CAAC;YAClC,cAAS,GAAG,SAAS,CAAC;YACtB,mBAAc,GAAG,cAAc,CAAC;YAOvC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACzD,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;YACD,MAAM,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAC,GAAG,OAAO,CAAC;YAChD,uBAAA,IAAI,qBAAe,CAAC,CAAC,UAAU,MAAA,CAAC;YAChC,uBAAA,IAAI,kBAAY,CAAC,CAAC,OAAO,MAAA,CAAC;YAC1B,uBAAA,IAAI,mBAAa,CAAC,CAAC,QAAQ,MAAA,CAAC;YAE5B,uBAAA,IAAI,eAAS,GAAG,IAAI,EAAE,MAAA,CAAC;YACvB,uBAAA,IAAI,iBAAW,IAAI,MAAA,CAAC;YACpB,uBAAA,IAAI,4BAAsB,KAAK,MAAA,CAAC;QAClC,CAAC;QAED,SAAS,CAAC,KAAa,EAAE,QAAkB,EAAE,WAAqB;YAChE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,wBAAwB;YACtB,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;QAED,cAAc;YACZ,uBAAA,IAAI,2BAAqB,IAAI,MAAA,CAAC;QAChC,CAAC;QAED,IAAI,MAAM;YACR,OAAO,uBAAA,IAAI,qBAAQ,CAAC;QACtB,CAAC;QAED,IAAI,aAAa;YACf,OAAO,uBAAA,IAAI,qBAAQ,CAAC;QACtB,CAAC;QAED,IAAI,UAAU;YACZ,OAAO,uBAAA,IAAI,qBAAQ,CAAC;QACtB,CAAC;QAED,IAAI,IAAI;YACN,OAAO,uBAAA,IAAI,mBAAM,CAAC;QACpB,CAAC;QAED,IAAI,UAAU;YACZ,OAAO,uBAAA,IAAI,yBAAY,CAAC;QAC1B,CAAC;QAED,IAAI,gBAAgB;YAClB,OAAO,uBAAA,IAAI,yBAAY,IAAI,uBAAA,IAAI,+BAAkB,CAAC;QACpD,CAAC;QAED,IAAI,SAAS;YACX,OAAO,uBAAA,IAAI,wBAAW,CAAC;QACzB,CAAC;QAED,YAAY;YACV,OAAO,uBAAA,IAAI,gCAAmB,CAAC,CAAC,CAAC,CAAC,uBAAA,IAAI,qBAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,CAAC;QAED,IAAI,WAAW;YACb,OAAO,CAAC,uBAAA,IAAI,yBAAY,IAAI,CAAC,uBAAA,IAAI,+BAAkB,CAAC;QACtD,CAAC;QAED,IAAI,OAAO;YACT,OAAO,uBAAA,IAAI,sBAAS,CAAC;QACvB,CAAC;QAED,IAAI,QAAQ;YACV,OAAO,uBAAA,IAAI,uBAAU,CAAC;QACxB,CAAC;QAED,IAAI,UAAU;YACZ,OAAO,uBAAA,IAAI,gCAAmB,CAAC,CAAC,CAAC,EAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAK,CAAC,IAAI,CAAC;QAChE,CAAC;QAED,IAAI,YAAY;YACd,OAAO,uBAAA,IAAI,iCAAoB,CAAC;QAClC,CAAC;QAED,IAAI,YAAY,CAAC,KAAK;YACpB,IAAI,KAAK,EAAE,CAAC;gBACV,uBAAA,IAAI,6BAAuB,IAAI,MAAA,CAAC;YAClC,CAAC;QACH,CAAC;QAED,eAAe;YACb,uBAAA,IAAI,6BAAuB,IAAI,MAAA,CAAC;QAClC,CAAC;QAED,IAAI,SAAS;YACX,OAAO,KAAK,CAAC;QACf,CAAC;KACF;;;;;;;;;;IAlGiB,OAAI,GAAG,IAAK;IACZ,kBAAe,GAAG,eAAgB;IAClC,YAAS,GAAG,SAAU;IACtB,iBAAc,GAAG,cAAe;OA+FjD,CAAC;AAEF,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,EAAE;IAC3C,SAAS,EAAE,kBAAkB;IAC7B,wBAAwB,EAAE,kBAAkB;IAC5C,cAAc,EAAE,kBAAkB;IAClC,MAAM,EAAE,kBAAkB;IAC1B,aAAa,EAAE,kBAAkB;IACjC,UAAU,EAAE,kBAAkB;IAC9B,IAAI,EAAE,kBAAkB;IACxB,UAAU,EAAE,kBAAkB;IAC9B,gBAAgB,EAAE,kBAAkB;IACpC,SAAS,EAAE,kBAAkB;IAC7B,YAAY,EAAE,kBAAkB;IAChC,WAAW,EAAE,kBAAkB;IAC/B,OAAO,EAAE,kBAAkB;IAC3B,QAAQ,EAAE,kBAAkB;IAC5B,UAAU,EAAE,kBAAkB;IAC9B,YAAY,EAAE,kBAAkB;IAChC,eAAe,EAAE,kBAAkB;IACnC,SAAS,EAAE,kBAAkB;CAC9B,CAAC,CAAC;AAIH,uFAAuF;AACvF,MAAM,eAAe,SAAG,MAAM,WAC5B,SAAQ,SAAS;QAKjB,YAAY,IAAY,EAAE,UAA8B,EAAE;YACxD,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAHvB,sCAAkB;YAIhB,uBAAA,IAAI,uBAAW,OAAO,EAAE,MAAM,IAAI,IAAI,MAAA,CAAC;QACzC,CAAC;QAED,eAAe,CACb,KAAa,EACb,QAAkB,EAClB,WAAqB,EACrB,OAAW;YAEX,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,MAAM;YACR,OAAO,uBAAA,IAAI,2BAAS,CAAC;QACvB,CAAC;KACF;;OAAA,CAAC;AAEF,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,SAAS,EAAE;IACjD,MAAM,EAAE,kBAAkB;CAC3B,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,SAAmC,CAAC;AAClE,MAAM,2BAA2B,GAC/B,eAA+C,CAAC;AAClD,OAAO,EACL,qBAAqB,IAAI,KAAK,EAC9B,qBAAqB,IAAI,SAAS,EAClC,2BAA2B,IAAI,WAAW,EAC1C,2BAA2B,IAAI,eAAe,GAC/C,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * This is a basic implementation of an EventTarget, Event and CustomEvent.\n *\n * This is not fully spec compliant (e.g. validation),\n * but should work well enough for our use cases.\n *\n * @see https://dom.spec.whatwg.org/#eventtarget\n * @see https://dom.spec.whatwg.org/#event\n * @see https://dom.spec.whatwg.org/#customevent\n */\n\nexport interface EventTargetShimMeta {\n  /**\n   * The event target parent represents the previous event target for an event\n   * in capture phase and the next event target for a bubbling event.\n   * Note that this is not the element parent\n   */\n  __eventTargetParent: globalThis.EventTarget | undefined;\n  /**\n   * The host event target/element of this event target, if this event target\n   * is inside a Shadow DOM.\n   */\n  __host: globalThis.EventTarget | undefined;\n}\n\nconst isCaptureEventListener = (\n  options: undefined | AddEventListenerOptions | boolean\n) => (typeof options === 'boolean' ? options : options?.capture ?? false);\n\n// Event phases\nconst NONE = 0;\nconst CAPTURING_PHASE = 1;\nconst AT_TARGET = 2;\nconst BUBBLING_PHASE = 3;\n\n// Shim the global EventTarget object\nclass EventTarget implements globalThis.EventTarget, EventTargetShimMeta {\n  private __eventListeners = new Map<\n    string,\n    Map<EventListenerOrEventListenerObject, AddEventListenerOptions>\n  >();\n  private __captureEventListeners = new Map<\n    string,\n    Map<EventListenerOrEventListenerObject, AddEventListenerOptions>\n  >();\n  __eventTargetParent: EventTarget | undefined;\n  __host: EventTarget | undefined;\n\n  addEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject | null,\n    options?: AddEventListenerOptions | boolean\n  ): void {\n    if (callback === undefined || callback === null) {\n      return;\n    }\n    const eventListenersMap = isCaptureEventListener(options)\n      ? this.__captureEventListeners\n      : this.__eventListeners;\n    let eventListeners = eventListenersMap.get(type);\n    if (eventListeners === undefined) {\n      eventListeners = new Map();\n      eventListenersMap.set(type, eventListeners);\n    } else if (eventListeners.has(callback)) {\n      return;\n    }\n\n    const normalizedOptions =\n      typeof options === 'object' && options ? options : {};\n    normalizedOptions.signal?.addEventListener('abort', () =>\n      this.removeEventListener(type, callback, options)\n    );\n    eventListeners.set(callback, normalizedOptions ?? {});\n  }\n  removeEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject | null,\n    options?: EventListenerOptions | boolean\n  ): void {\n    if (callback === undefined || callback === null) {\n      return;\n    }\n    const eventListenersMap = isCaptureEventListener(options)\n      ? this.__captureEventListeners\n      : this.__eventListeners;\n    const eventListeners = eventListenersMap.get(type);\n    if (eventListeners !== undefined) {\n      eventListeners.delete(callback);\n      if (!eventListeners.size) {\n        eventListenersMap.delete(type);\n      }\n    }\n  }\n  dispatchEvent(event: Event): boolean {\n    const composedPath: EventTarget[] = [this];\n    let parent = this.__eventTargetParent;\n    if (event.composed) {\n      while (parent) {\n        composedPath.push(parent);\n        parent = parent.__eventTargetParent;\n      }\n    } else {\n      // If the event is not composed and the event was dispatched inside\n      // shadow DOM, we need to stop before the host of the shadow DOM.\n      while (parent && parent !== this.__host) {\n        composedPath.push(parent);\n        parent = parent.__eventTargetParent;\n      }\n    }\n\n    // We need to patch various properties that would either be empty or wrong\n    // in this scenario.\n    let stopPropagation = false;\n    let stopImmediatePropagation = false;\n    let eventPhase = NONE;\n    let target: EventTarget | null = null;\n    let tmpTarget: EventTarget | null = null;\n    let currentTarget: EventTarget | null = null;\n    const originalStopPropagation = event.stopPropagation;\n    const originalStopImmediatePropagation = event.stopImmediatePropagation;\n    Object.defineProperties(event, {\n      target: {\n        get() {\n          return target ?? tmpTarget;\n        },\n        ...enumerableProperty,\n      },\n      srcElement: {\n        get() {\n          return event.target;\n        },\n        ...enumerableProperty,\n      },\n      currentTarget: {\n        get() {\n          return currentTarget;\n        },\n        ...enumerableProperty,\n      },\n      eventPhase: {\n        get() {\n          return eventPhase;\n        },\n        ...enumerableProperty,\n      },\n      composedPath: {\n        value: () => composedPath,\n        ...enumerableProperty,\n      },\n      stopPropagation: {\n        value: () => {\n          stopPropagation = true;\n          originalStopPropagation.call(event);\n        },\n        ...enumerableProperty,\n      },\n      stopImmediatePropagation: {\n        value: () => {\n          stopImmediatePropagation = true;\n          originalStopImmediatePropagation.call(event);\n        },\n        ...enumerableProperty,\n      },\n    });\n\n    // An event handler can either be a function, an object with a handleEvent\n    // method or null. This function takes care to call the event handler\n    // correctly.\n    const invokeEventListener = (\n      listener: EventListenerOrEventListenerObject,\n      options: AddEventListenerOptions,\n      eventListenerMap: Map<\n        EventListenerOrEventListenerObject,\n        AddEventListenerOptions\n      >\n    ) => {\n      if (typeof listener === 'function') {\n        listener(event);\n      } else if (typeof listener?.handleEvent === 'function') {\n        listener.handleEvent(event);\n      }\n      if (options.once) {\n        eventListenerMap.delete(listener);\n      }\n    };\n    // When an event is finished being dispatched, which can be after the event\n    // tree has been traversed or stopPropagation/stopImmediatePropagation has\n    // been called. Once that is the case, the currentTarget and eventPhase\n    // need to be reset and a value, representing whether the event has not\n    // been prevented, needs to be returned.\n    const finishDispatch = () => {\n      currentTarget = null;\n      eventPhase = NONE;\n      return !event.defaultPrevented;\n    };\n\n    // An event starts with the capture order, where it starts from the top.\n    // This is done even if bubbles is set to false, which is the default.\n    const captureEventPath = composedPath.slice().reverse();\n    // If the event target, which dispatches the event, is either in the light DOM\n    // or the event is not composed, the target is always itself. If that is not\n    // the case, the target needs to be retargeted: https://dom.spec.whatwg.org/#retarget\n    target = !this.__host || !event.composed ? this : null;\n    const retarget = (eventTargets: EventTarget[]) => {\n      // eslint-disable-next-line @typescript-eslint/no-this-alias\n      tmpTarget = this;\n      while (tmpTarget.__host && eventTargets.includes(tmpTarget.__host)) {\n        tmpTarget = tmpTarget.__host;\n      }\n    };\n    for (const eventTarget of captureEventPath) {\n      if (!target && (!tmpTarget || tmpTarget === eventTarget.__host)) {\n        retarget(captureEventPath.slice(captureEventPath.indexOf(eventTarget)));\n      }\n      currentTarget = eventTarget;\n      eventPhase = eventTarget === event.target ? AT_TARGET : CAPTURING_PHASE;\n      const captureEventListeners = eventTarget.__captureEventListeners.get(\n        event.type\n      );\n      if (captureEventListeners) {\n        for (const [listener, options] of captureEventListeners) {\n          invokeEventListener(listener, options, captureEventListeners);\n          if (stopImmediatePropagation) {\n            // Event.stopImmediatePropagation() stops any following invocation\n            // of an event handler even on the same event target.\n            return finishDispatch();\n          }\n        }\n      }\n      if (stopPropagation) {\n        // Event.stopPropagation() stops any following invocation\n        // of an event handler for any following event targets.\n        return finishDispatch();\n      }\n    }\n\n    const bubbleEventPath = event.bubbles ? composedPath : [this];\n    tmpTarget = null;\n    for (const eventTarget of bubbleEventPath) {\n      if (\n        !target &&\n        (!tmpTarget || eventTarget === (tmpTarget as EventTarget).__host)\n      ) {\n        retarget(\n          bubbleEventPath.slice(0, bubbleEventPath.indexOf(eventTarget) + 1)\n        );\n      }\n      currentTarget = eventTarget;\n      eventPhase = eventTarget === event.target ? AT_TARGET : BUBBLING_PHASE;\n      const captureEventListeners = eventTarget.__eventListeners.get(\n        event.type\n      );\n      if (captureEventListeners) {\n        for (const [listener, options] of captureEventListeners) {\n          invokeEventListener(listener, options, captureEventListeners);\n          if (stopImmediatePropagation) {\n            // Event.stopImmediatePropagation() stops any following invocation\n            // of an event handler even on the same event target.\n            return finishDispatch();\n          }\n        }\n      }\n      if (stopPropagation) {\n        // Event.stopPropagation() stops any following invocation\n        // of an event handler for any following event targets.\n        return finishDispatch();\n      }\n    }\n    return finishDispatch();\n  }\n}\n\nconst EventTargetShimWithRealType =\n  EventTarget as object as typeof globalThis.EventTarget;\nexport {\n  EventTargetShimWithRealType as EventTarget,\n  EventTargetShimWithRealType as EventTargetShim,\n};\n\n/* Adapted from Node.js https://github.com/nodejs/node/blob/main/lib/internal/event_target.js */\n\ntype EventInterface = Event;\n\nconst enumerableProperty: Record<string, unknown> = {__proto__: null};\nenumerableProperty.enumerable = true;\nObject.freeze(enumerableProperty);\n\n// TODO: Remove this when we remove support for vm modules (--experimental-vm-modules).\nconst EventShim = class Event implements EventInterface {\n  #cancelable = false;\n  #bubbles = false;\n  #composed = false;\n  #defaultPrevented = false;\n  #timestamp = Date.now();\n  #propagationStopped = false;\n  #type: string;\n  #target: globalThis.EventTarget | null;\n  #isBeingDispatched: boolean;\n  readonly NONE = NONE;\n  readonly CAPTURING_PHASE = CAPTURING_PHASE;\n  readonly AT_TARGET = AT_TARGET;\n  readonly BUBBLING_PHASE = BUBBLING_PHASE;\n  static readonly NONE = NONE;\n  static readonly CAPTURING_PHASE = CAPTURING_PHASE;\n  static readonly AT_TARGET = AT_TARGET;\n  static readonly BUBBLING_PHASE = BUBBLING_PHASE;\n\n  constructor(type: string, options: EventInit = {}) {\n    if (arguments.length === 0)\n      throw new Error(`The type argument must be specified`);\n    if (typeof options !== 'object' || !options) {\n      throw new Error(`The \"options\" argument must be an object`);\n    }\n    const {bubbles, cancelable, composed} = options;\n    this.#cancelable = !!cancelable;\n    this.#bubbles = !!bubbles;\n    this.#composed = !!composed;\n\n    this.#type = `${type}`;\n    this.#target = null;\n    this.#isBeingDispatched = false;\n  }\n\n  initEvent(_type: string, _bubbles?: boolean, _cancelable?: boolean): void {\n    throw new Error('Method not implemented.');\n  }\n\n  stopImmediatePropagation() {\n    this.stopPropagation();\n  }\n\n  preventDefault() {\n    this.#defaultPrevented = true;\n  }\n\n  get target(): globalThis.EventTarget | null {\n    return this.#target;\n  }\n\n  get currentTarget(): globalThis.EventTarget | null {\n    return this.#target;\n  }\n\n  get srcElement(): globalThis.EventTarget | null {\n    return this.#target;\n  }\n\n  get type(): string {\n    return this.#type;\n  }\n\n  get cancelable(): boolean {\n    return this.#cancelable;\n  }\n\n  get defaultPrevented(): boolean {\n    return this.#cancelable && this.#defaultPrevented;\n  }\n\n  get timeStamp(): number {\n    return this.#timestamp;\n  }\n\n  composedPath(): globalThis.EventTarget[] {\n    return this.#isBeingDispatched ? [this.#target!] : [];\n  }\n\n  get returnValue(): boolean {\n    return !this.#cancelable || !this.#defaultPrevented;\n  }\n\n  get bubbles(): boolean {\n    return this.#bubbles;\n  }\n\n  get composed(): boolean {\n    return this.#composed;\n  }\n\n  get eventPhase(): number {\n    return this.#isBeingDispatched ? Event.AT_TARGET : Event.NONE;\n  }\n\n  get cancelBubble(): boolean {\n    return this.#propagationStopped;\n  }\n\n  set cancelBubble(value) {\n    if (value) {\n      this.#propagationStopped = true;\n    }\n  }\n\n  stopPropagation(): void {\n    this.#propagationStopped = true;\n  }\n\n  get isTrusted(): boolean {\n    return false;\n  }\n};\n\nObject.defineProperties(EventShim.prototype, {\n  initEvent: enumerableProperty,\n  stopImmediatePropagation: enumerableProperty,\n  preventDefault: enumerableProperty,\n  target: enumerableProperty,\n  currentTarget: enumerableProperty,\n  srcElement: enumerableProperty,\n  type: enumerableProperty,\n  cancelable: enumerableProperty,\n  defaultPrevented: enumerableProperty,\n  timeStamp: enumerableProperty,\n  composedPath: enumerableProperty,\n  returnValue: enumerableProperty,\n  bubbles: enumerableProperty,\n  composed: enumerableProperty,\n  eventPhase: enumerableProperty,\n  cancelBubble: enumerableProperty,\n  stopPropagation: enumerableProperty,\n  isTrusted: enumerableProperty,\n});\n\ntype CustomEventInterface = CustomEvent;\n\n// TODO: Remove this when we remove support for vm modules (--experimental-vm-modules).\nconst CustomEventShim = class CustomEvent<T>\n  extends EventShim\n  implements CustomEventInterface\n{\n  #detail: T | null;\n\n  constructor(type: string, options: CustomEventInit<T> = {}) {\n    super(type, options);\n    this.#detail = options?.detail ?? null;\n  }\n\n  initCustomEvent(\n    _type: string,\n    _bubbles?: boolean,\n    _cancelable?: boolean,\n    _detail?: T\n  ): void {\n    throw new Error('Method not implemented.');\n  }\n\n  get detail(): T {\n    return this.#detail!;\n  }\n};\n\nObject.defineProperties(CustomEventShim.prototype, {\n  detail: enumerableProperty,\n});\n\nconst EventShimWithRealType = EventShim as object as typeof Event;\nconst CustomEventShimWithRealType =\n  CustomEventShim as object as typeof CustomEvent;\nexport {\n  EventShimWithRealType as Event,\n  EventShimWithRealType as EventShim,\n  CustomEventShimWithRealType as CustomEvent,\n  CustomEventShimWithRealType as CustomEventShim,\n};\n"]}