{"version": 3, "file": "lit-element.js", "sources": ["src/lit-element.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * The main LitElement module, which defines the {@linkcode LitElement} base\n * class and related APIs.\n *\n * LitElement components can define a template and a set of observed\n * properties. Changing an observed property triggers a re-render of the\n * element.\n *\n * Import {@linkcode LitElement} and {@linkcode html} from this module to\n * create a component:\n *\n *  ```js\n * import {LitElement, html} from 'lit-element';\n *\n * class MyElement extends LitElement {\n *\n *   // Declare observed properties\n *   static get properties() {\n *     return {\n *       adjective: {}\n *     }\n *   }\n *\n *   constructor() {\n *     this.adjective = 'awesome';\n *   }\n *\n *   // Define the element's template\n *   render() {\n *     return html`<p>your ${adjective} template here</p>`;\n *   }\n * }\n *\n * customElements.define('my-element', MyElement);\n * ```\n *\n * `LitElement` extends {@linkcode ReactiveElement} and adds lit-html\n * templating. The `ReactiveElement` class is provided for users that want to\n * build their own custom element base classes that don't use lit-html.\n *\n * @packageDocumentation\n */\nimport {PropertyValues, ReactiveElement} from '@lit/reactive-element';\nimport {render, RenderOptions, noChange, RootPart} from 'lit-html';\nexport * from '@lit/reactive-element';\nexport * from 'lit-html';\n\nimport {LitUnstable} from 'lit-html';\nimport {ReactiveUnstable} from '@lit/reactive-element';\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace Unstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry =\n      | LitUnstable.DebugLog.Entry\n      | ReactiveUnstable.DebugLog.Entry;\n  }\n}\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = <P extends PropertyKey>(\n  prop: P,\n  _obj: unknown\n): P => prop;\n\nconst DEV_MODE = true;\n// Allows minifiers to rename references to globalThis\nconst global = globalThis;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  global.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (\n      !global.litIssuedWarnings!.has(warning) &&\n      !global.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      global.litIssuedWarnings!.add(warning);\n    }\n  };\n}\n\n/**\n * Base element class that manages element properties and attributes, and\n * renders a lit-html template.\n *\n * To define a component, subclass `LitElement` and implement a\n * `render` method to provide the component's template. Define properties\n * using the {@linkcode LitElement.properties properties} property or the\n * {@linkcode property} decorator.\n */\nexport class LitElement extends ReactiveElement {\n  // This property needs to remain unminified.\n  static ['_$litElement$'] = true;\n\n  /**\n   * @category rendering\n   */\n  readonly renderOptions: RenderOptions = {host: this};\n\n  private __childPart: RootPart | undefined = undefined;\n\n  /**\n   * @category rendering\n   */\n  protected override createRenderRoot() {\n    const renderRoot = super.createRenderRoot();\n    // When adoptedStyleSheets are shimmed, they are inserted into the\n    // shadowRoot by createRenderRoot. Adjust the renderBefore node so that\n    // any styles in Lit content render before adoptedStyleSheets. This is\n    // important so that adoptedStyleSheets have precedence over styles in\n    // the shadowRoot.\n    this.renderOptions.renderBefore ??= renderRoot!.firstChild as ChildNode;\n    return renderRoot;\n  }\n\n  /**\n   * Updates the element. This method reflects property values to attributes\n   * and calls `render` to render DOM via lit-html. Setting properties inside\n   * this method will *not* trigger another update.\n   * @param changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected override update(changedProperties: PropertyValues) {\n    // Setting properties in `render` should not trigger an update. Since\n    // updates are allowed after super.update, it's important to call `render`\n    // before that.\n    const value = this.render();\n    if (!this.hasUpdated) {\n      this.renderOptions.isConnected = this.isConnected;\n    }\n    super.update(changedProperties);\n    this.__childPart = render(value, this.renderRoot, this.renderOptions);\n  }\n\n  /**\n   * Invoked when the component is added to the document's DOM.\n   *\n   * In `connectedCallback()` you should setup tasks that should only occur when\n   * the element is connected to the document. The most common of these is\n   * adding event listeners to nodes external to the element, like a keydown\n   * event handler added to the window.\n   *\n   * ```ts\n   * connectedCallback() {\n   *   super.connectedCallback();\n   *   addEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * Typically, anything done in `connectedCallback()` should be undone when the\n   * element is disconnected, in `disconnectedCallback()`.\n   *\n   * @category lifecycle\n   */\n  override connectedCallback() {\n    super.connectedCallback();\n    this.__childPart?.setConnected(true);\n  }\n\n  /**\n   * Invoked when the component is removed from the document's DOM.\n   *\n   * This callback is the main signal to the element that it may no longer be\n   * used. `disconnectedCallback()` should ensure that nothing is holding a\n   * reference to the element (such as event listeners added to nodes external\n   * to the element), so that it is free to be garbage collected.\n   *\n   * ```ts\n   * disconnectedCallback() {\n   *   super.disconnectedCallback();\n   *   window.removeEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * An element may be re-connected after being disconnected.\n   *\n   * @category lifecycle\n   */\n  override disconnectedCallback() {\n    super.disconnectedCallback();\n    this.__childPart?.setConnected(false);\n  }\n\n  /**\n   * Invoked on each update to perform rendering tasks. This method may return\n   * any value renderable by lit-html's `ChildPart` - typically a\n   * `TemplateResult`. Setting properties inside this method will *not* trigger\n   * the element to update.\n   * @category rendering\n   */\n  protected render(): unknown {\n    return noChange;\n  }\n}\n\n/**\n * Ensure this class is marked as `finalized` as an optimization ensuring\n * it will not needlessly try to `finalize`.\n *\n * Note this property name is a string to prevent breaking Closure JS Compiler\n * optimizations. See @lit/reactive-element for more information.\n */\n(LitElement as unknown as Record<string, unknown>)[\n  JSCompiler_renameProperty('finalized', LitElement)\n] = true;\n\n// Install hydration if available\nglobal.litElementHydrateSupport?.({LitElement});\n\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n  ? global.litElementPolyfillSupportDevMode\n  : global.litElementPolyfillSupport;\npolyfillSupport?.({LitElement});\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LE object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-html, since this module re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LE = {\n  _$attributeToProperty: (\n    el: LitElement,\n    name: string,\n    value: string | null\n  ) => {\n    // eslint-disable-next-line\n    (el as any)._$attributeToProperty(name, value);\n  },\n  // eslint-disable-next-line\n  _$changedProperties: (el: LitElement) => (el as any)._$changedProperties,\n};\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for LitElement usage.\n(global.litElementVersions ??= []).push('4.2.1');\nif (DEV_MODE && global.litElementVersions.length > 1) {\n  queueMicrotask(() => {\n    issueWarning!(\n      'multiple-versions',\n      `Multiple versions of Lit loaded. Loading multiple versions ` +\n        `is not recommended.`\n    );\n  });\n}\n"], "names": ["global", "globalThis", "LitElement", "ReactiveElement", "constructor", "this", "renderOptions", "host", "__child<PERSON><PERSON>", "undefined", "createRenderRoot", "renderRoot", "super", "renderBefore", "<PERSON><PERSON><PERSON><PERSON>", "update", "changedProperties", "value", "render", "hasUpdated", "isConnected", "connectedCallback", "setConnected", "disconnectedCallback", "noChange", "litElementHydrateSupport", "polyfillSupport", "litElementPolyfillSupport", "_$LE", "_$attributeToProperty", "el", "name", "_$changedProperties", "litElementVersions", "push"], "mappings": ";;;;;GAuFA,MAOMA,EAASC,WAmCT,MAAOC,UAAmBC,EAAhC,WAAAC,uBAOWC,KAAAC,cAA+B,CAACC,KAAMF,MAEvCA,KAAWG,UAAyBC,CA8F7C,CAzFoB,gBAAAC,GACjB,MAAMC,EAAaC,MAAMF,mBAOzB,OADAL,KAAKC,cAAcO,eAAiBF,EAAYG,WACzCH,CACR,CASkB,MAAAI,CAAOC,GAIxB,MAAMC,EAAQZ,KAAKa,SACdb,KAAKc,aACRd,KAAKC,cAAcc,YAAcf,KAAKe,aAExCR,MAAMG,OAAOC,GACbX,KAAKG,KAAcU,EAAOD,EAAOZ,KAAKM,WAAYN,KAAKC,cACxD,CAsBQ,iBAAAe,GACPT,MAAMS,oBACNhB,KAAKG,MAAac,cAAa,EAChC,CAqBQ,oBAAAC,GACPX,MAAMW,uBACNlB,KAAKG,MAAac,cAAa,EAChC,CASS,MAAAJ,GACR,OAAOM,CACR,EApGMtB,EAAgB,eAAI,EA8G5BA,EAC2B,cACxB,EAGJF,EAAOyB,2BAA2B,CAACvB,eAGnC,MAAMwB,EAEF1B,EAAO2B,0BACXD,IAAkB,CAACxB,eAoBN,MAAA0B,EAAO,CAClBC,KAAuB,CACrBC,EACAC,EACAd,KAGCa,EAAWD,KAAsBE,EAAMd,EAAM,EAGhDe,KAAsBF,GAAoBA,EAAWE,OAKtDhC,EAAOiC,qBAAuB,IAAIC,KAAK"}