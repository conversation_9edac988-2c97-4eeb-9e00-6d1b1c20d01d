{"version": 3, "file": "unsafe-html.js", "sourceRoot": "", "sources": ["../../src/directives/unsafe-html.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,OAAO,EAAkB,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AACjE,OAAO,EAAC,SAAS,EAAE,SAAS,EAAY,QAAQ,EAAC,MAAM,iBAAiB,CAAC;AAEzE,MAAM,WAAW,GAAG,CAAC,CAAC;AAEtB,MAAM,OAAO,mBAAoB,SAAQ,SAAS;IAOhD,YAAY,QAAkB;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAJV,WAAM,GAAY,OAAO,CAAC;QAKhC,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CACb,GACG,IAAI,CAAC,WAA0C,CAAC,aACnD,uCAAuC,CACxC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAmE;QACxE,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YACvC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;QAC/B,CAAC;QACD,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,GACG,IAAI,CAAC,WAA0C,CAAC,aACnD,mCAAmC,CACpC,CAAC;QACJ,CAAC;QACD,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,MAAM,OAAO,GAAG,CAAC,KAAK,CAAoC,CAAC;QAC3D,8DAA8D;QAC7D,OAAe,CAAC,GAAG,GAAG,OAAO,CAAC;QAC/B,iEAAiE;QACjE,wDAAwD;QACxD,OAAO,CAAC,IAAI,CAAC,eAAe,GAAG;YAC7B,qEAAqE;YACrE,uEAAuE;YACvE,4CAA4C;YAC5C,CAAC,YAAY,CAAC,EAAG,IAAI,CAAC,WAA0C;iBAC7D,UAAmB;YACtB,OAAO;YACP,MAAM,EAAE,EAAE;SACX,CAAC,CAAC;IACL,CAAC;;AAlDM,iCAAa,GAAG,YAAY,AAAf,CAAgB;AAC7B,8BAAU,GAAG,WAAW,AAAd,CAAe;AAoDlC;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,SAAS,CAAC,mBAAmB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {nothing, TemplateResult, noChange} from '../lit-html.js';\nimport {directive, Directive, PartInfo, PartType} from '../directive.js';\n\nconst HTML_RESULT = 1;\n\nexport class UnsafeHTMLDirective extends Directive {\n  static directiveName = 'unsafeHTML';\n  static resultType = HTML_RESULT;\n\n  private _value: unknown = nothing;\n  private _templateResult?: TemplateResult;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (partInfo.type !== PartType.CHILD) {\n      throw new Error(\n        `${\n          (this.constructor as typeof UnsafeHTMLDirective).directiveName\n        }() can only be used in child bindings`\n      );\n    }\n  }\n\n  render(value: string | typeof nothing | typeof noChange | undefined | null) {\n    if (value === nothing || value == null) {\n      this._templateResult = undefined;\n      return (this._value = value);\n    }\n    if (value === noChange) {\n      return value;\n    }\n    if (typeof value != 'string') {\n      throw new Error(\n        `${\n          (this.constructor as typeof UnsafeHTMLDirective).directiveName\n        }() called with a non-string value`\n      );\n    }\n    if (value === this._value) {\n      return this._templateResult;\n    }\n    this._value = value;\n    const strings = [value] as unknown as TemplateStringsArray;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (strings as any).raw = strings;\n    // WARNING: impersonating a TemplateResult like this is extremely\n    // dangerous. Third-party directives should not do this.\n    return (this._templateResult = {\n      // Cast to a known set of integers that satisfy ResultType so that we\n      // don't have to export ResultType and possibly encourage this pattern.\n      // This property needs to remain unminified.\n      ['_$litType$']: (this.constructor as typeof UnsafeHTMLDirective)\n        .resultType as 1 | 2,\n      strings,\n      values: [],\n    });\n  }\n}\n\n/**\n * Renders the result as HTML, rather than text.\n *\n * The values `undefined`, `null`, and `nothing`, will all result in no content\n * (empty string) being rendered.\n *\n * Note, this is unsafe to use with any user-provided input that hasn't been\n * sanitized or escaped, as it may lead to cross-site-scripting\n * vulnerabilities.\n */\nexport const unsafeHTML = directive(UnsafeHTMLDirective);\n"]}