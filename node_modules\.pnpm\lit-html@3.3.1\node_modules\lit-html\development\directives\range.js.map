{"version": 3, "file": "range.js", "sourceRoot": "", "sources": ["../../src/directives/range.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAwBH,MAAM,SAAS,CAAC,CAAC,KAAK,CAAC,UAAkB,EAAE,GAAY,EAAE,IAAI,GAAG,CAAC;IAC/D,MAAM,KAAK,GAAG,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;IACjD,GAAG,KAAK,UAAU,CAAC;IACnB,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;QAC5D,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Returns an iterable of integers from `start` to `end` (exclusive)\n * incrementing by `step`.\n *\n * If `start` is omitted, the range starts at `0`. `step` defaults to `1`.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${map(range(8), () => html`<div class=\"cell\"></div>`)}\n *   `;\n * }\n * ```\n */\nexport function range(end: number): Iterable<number>;\nexport function range(\n  start: number,\n  end: number,\n  step?: number\n): Iterable<number>;\nexport function* range(startOrEnd: number, end?: number, step = 1) {\n  const start = end === undefined ? 0 : startOrEnd;\n  end ??= startOrEnd;\n  for (let i = start; step > 0 ? i < end : end < i; i += step) {\n    yield i;\n  }\n}\n"]}